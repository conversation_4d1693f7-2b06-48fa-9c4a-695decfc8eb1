using UnityEngine;
using System.Collections.Generic;

public class GrassParticleController : MonoBehaviour
{
    public Mesh mesh;
    public Material grassParticleMaterial;
    public int layer = 11;

    public float dx = 10;
    public float dz = 10;
    public float scale = 10;
    public float yOffset = 8;

    public float popoutRate = 0.01f;
    public float popoutDistance = 0.5f;

    private Terrain terrain;
    private List<Matrix4x4> grassMatrices;
    private MaterialPropertyBlock propertyBlock;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        InitializeGrassSystem();
    }

    void InitializeGrassSystem()
    {
        // Find the terrain in the scene
        terrain = FindObjectOfType<Terrain>();
        if (terrain == null)
        {
            Debug.LogError("No terrain found in the scene!");
            return;
        }

        // Initialize property block for rendering
        propertyBlock = new MaterialPropertyBlock();

        // Generate grass positions
        GenerateGrassPositions();
    }

    void GenerateGrassPositions()
    {
        if (terrain == null || mesh == null) return;

        grassMatrices = new List<Matrix4x4>();

        // Get terrain bounds
        TerrainData terrainData = terrain.terrainData;
        Vector3 terrainSize = terrainData.size;
        Vector3 terrainPosition = terrain.transform.position;

        // Calculate how many grass instances we need based on spacing
        int grassCountX = Mathf.FloorToInt(terrainSize.x / dx);
        int grassCountZ = Mathf.FloorToInt(terrainSize.z / dz);

        // Generate grass positions across the terrain
        for (int x = 0; x < grassCountX; x++)
        {
            for (int z = 0; z < grassCountZ; z++)
            {
                // Calculate world position
                float worldX = terrainPosition.x + (x * dx) + Random.Range(-popoutDistance, popoutDistance);
                float worldZ = terrainPosition.z + (z * dz) + Random.Range(-popoutDistance, popoutDistance);

                // Sample terrain height at this position
                float normalizedX = (worldX - terrainPosition.x) / terrainSize.x;
                float normalizedZ = (worldZ - terrainPosition.z) / terrainSize.z;

                // Clamp to terrain bounds
                normalizedX = Mathf.Clamp01(normalizedX);
                normalizedZ = Mathf.Clamp01(normalizedZ);

                float terrainHeight = terrainData.GetInterpolatedHeight(normalizedX, normalizedZ);
                float worldY = terrainPosition.y + terrainHeight + yOffset;

                // Random rotation for natural look
                float randomRotation = Random.Range(0f, 360f);
                Quaternion rotation = Quaternion.Euler(0, randomRotation, 0);

                // Random scale variation
                float scaleVariation = Random.Range(0.8f, 1.2f);
                Vector3 scaleVector = Vector3.one * scale * scaleVariation;

                // Create transformation matrix
                Vector3 position = new Vector3(worldX, worldY, worldZ);
                Matrix4x4 matrix = Matrix4x4.TRS(position, rotation, scaleVector);

                // Only add if we pass the popout rate check
                if (Random.value < popoutRate)
                {
                    grassMatrices.Add(matrix);
                }
            }
        }

        Debug.Log($"Generated {grassMatrices.Count} grass instances across terrain");
    }

    // Update is called once per frame
    void Update()
    {
        RenderGrass();
    }

    void RenderGrass()
    {
        if (grassMatrices == null || grassMatrices.Count == 0 || mesh == null || grassParticleMaterial == null)
            return;

        // Unity's Graphics.DrawMeshInstanced has a limit of 1023 instances per call
        const int maxInstancesPerBatch = 1023;

        for (int i = 0; i < grassMatrices.Count; i += maxInstancesPerBatch)
        {
            int instanceCount = Mathf.Min(maxInstancesPerBatch, grassMatrices.Count - i);
            Matrix4x4[] batch = new Matrix4x4[instanceCount];

            for (int j = 0; j < instanceCount; j++)
            {
                batch[j] = grassMatrices[i + j];
            }

            Graphics.DrawMeshInstanced(mesh, 0, grassParticleMaterial, batch, instanceCount, propertyBlock,
                UnityEngine.Rendering.ShadowCastingMode.On, true, layer);
        }
    }

    // Call this if you want to regenerate grass (useful for testing)
    [ContextMenu("Regenerate Grass")]
    public void RegenerateGrass()
    {
        GenerateGrassPositions();
    }
}
