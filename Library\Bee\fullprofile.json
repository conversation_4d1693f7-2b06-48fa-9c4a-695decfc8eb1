{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 781, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 781, "ts": 1751901149341083, "dur": 2701, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 781, "ts": 1751901149351274, "dur": 2438, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751901149044140, "dur": 6047, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901149050193, "dur": 66779, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901149116982, "dur": 28065, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 781, "ts": 1751901149353722, "dur": 22, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149041829, "dur": 9614, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149051447, "dur": 276548, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149052586, "dur": 3089, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149055684, "dur": 2161, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149057850, "dur": 364, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058216, "dur": 16, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058233, "dur": 56, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058291, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058293, "dur": 44, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058341, "dur": 36, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058378, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058380, "dur": 112, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058499, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058549, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058551, "dur": 46, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058600, "dur": 40, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058644, "dur": 44, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058691, "dur": 45, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058738, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058739, "dur": 43, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058784, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058786, "dur": 42, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058829, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058831, "dur": 42, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058875, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058879, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058927, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058929, "dur": 52, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058983, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149058984, "dur": 45, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059031, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059032, "dur": 93, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059130, "dur": 53, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059185, "dur": 2, "ph": "X", "name": "ProcessMessages 1357", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059187, "dur": 44, "ph": "X", "name": "ReadAsync 1357", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059233, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059235, "dur": 42, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059280, "dur": 45, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059328, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059329, "dur": 43, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059374, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059376, "dur": 42, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059421, "dur": 40, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059464, "dur": 48, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059514, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059515, "dur": 44, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059561, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059563, "dur": 42, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059607, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059608, "dur": 43, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059654, "dur": 44, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059700, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059701, "dur": 42, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059745, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059747, "dur": 41, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059791, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059793, "dur": 42, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059837, "dur": 45, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059884, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059886, "dur": 45, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059934, "dur": 43, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059979, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149059981, "dur": 43, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060026, "dur": 50, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060078, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060079, "dur": 50, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060131, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060132, "dur": 40, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060174, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060177, "dur": 45, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060223, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060225, "dur": 45, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060273, "dur": 45, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060319, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060321, "dur": 38, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060361, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060362, "dur": 48, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060416, "dur": 54, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060472, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060475, "dur": 49, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060526, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060528, "dur": 50, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060581, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060582, "dur": 40, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060625, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060626, "dur": 42, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060672, "dur": 44, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060718, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060720, "dur": 44, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060766, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060768, "dur": 42, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060811, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060813, "dur": 35, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149060852, "dur": 228, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061084, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061133, "dur": 1, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061135, "dur": 428, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061568, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061570, "dur": 121, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061695, "dur": 6, "ph": "X", "name": "ProcessMessages 4506", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061703, "dur": 59, "ph": "X", "name": "ReadAsync 4506", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061766, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061769, "dur": 43, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061818, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061858, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061862, "dur": 105, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061971, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149061974, "dur": 71, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062048, "dur": 2, "ph": "X", "name": "ProcessMessages 1631", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062051, "dur": 51, "ph": "X", "name": "ReadAsync 1631", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062105, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062107, "dur": 51, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062160, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062163, "dur": 59, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062224, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062227, "dur": 53, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062282, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062284, "dur": 56, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062344, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062346, "dur": 54, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062403, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062406, "dur": 45, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062454, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062457, "dur": 91, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062551, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062554, "dur": 57, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062614, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062617, "dur": 50, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062670, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062672, "dur": 46, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062722, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062724, "dur": 35, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062762, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062764, "dur": 46, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062812, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062815, "dur": 44, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062862, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062865, "dur": 46, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062913, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062915, "dur": 38, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062956, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062959, "dur": 38, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149062999, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063001, "dur": 45, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063049, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063051, "dur": 48, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063103, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063106, "dur": 59, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063168, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063171, "dur": 42, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063216, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063219, "dur": 90, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063313, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063315, "dur": 66, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063385, "dur": 2, "ph": "X", "name": "ProcessMessages 1386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063388, "dur": 39, "ph": "X", "name": "ReadAsync 1386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063430, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063432, "dur": 42, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063476, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063479, "dur": 55, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063536, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063538, "dur": 42, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063583, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063586, "dur": 43, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063632, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063635, "dur": 45, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063682, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063684, "dur": 43, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063730, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063732, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063777, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063779, "dur": 43, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063825, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063827, "dur": 60, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063891, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063893, "dur": 59, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063955, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149063958, "dur": 56, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064017, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064020, "dur": 55, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064077, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064079, "dur": 61, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064144, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064146, "dur": 62, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064214, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064217, "dur": 56, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064276, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064278, "dur": 50, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064332, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064335, "dur": 48, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064386, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064388, "dur": 41, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064432, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064434, "dur": 38, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064475, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064477, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064517, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064518, "dur": 56, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064577, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064579, "dur": 41, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064623, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064625, "dur": 48, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064675, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064677, "dur": 60, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064741, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064743, "dur": 51, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064797, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064799, "dur": 51, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064853, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064856, "dur": 47, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064907, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064910, "dur": 43, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149064958, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065005, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065007, "dur": 41, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065051, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065053, "dur": 42, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065099, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065101, "dur": 39, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065142, "dur": 1, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065145, "dur": 49, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065197, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065201, "dur": 62, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065265, "dur": 3, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065269, "dur": 39, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065310, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065312, "dur": 141, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065457, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065557, "dur": 1, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065559, "dur": 98, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065659, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065662, "dur": 51, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065715, "dur": 2, "ph": "X", "name": "ProcessMessages 1334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065718, "dur": 51, "ph": "X", "name": "ReadAsync 1334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065771, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065773, "dur": 45, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065820, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065821, "dur": 91, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065916, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065919, "dur": 62, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065983, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149065985, "dur": 50, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066036, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066038, "dur": 37, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066077, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066078, "dur": 38, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066119, "dur": 44, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066165, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066167, "dur": 36, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066206, "dur": 34, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066243, "dur": 47, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066293, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066294, "dur": 49, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066345, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066347, "dur": 44, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066393, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066395, "dur": 42, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066438, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066440, "dur": 38, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066481, "dur": 43, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066527, "dur": 39, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066570, "dur": 36, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066607, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066609, "dur": 41, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066653, "dur": 44, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066700, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066701, "dur": 43, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066746, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066748, "dur": 35, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066786, "dur": 42, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066830, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066832, "dur": 35, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066868, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066870, "dur": 31, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066903, "dur": 31, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066937, "dur": 42, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066980, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149066982, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067028, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067029, "dur": 30, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067061, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067063, "dur": 41, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067108, "dur": 44, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067154, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067155, "dur": 34, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067191, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067193, "dur": 42, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067238, "dur": 43, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067282, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067284, "dur": 43, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067330, "dur": 35, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067369, "dur": 39, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067410, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067412, "dur": 179, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067596, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149067639, "dur": 388, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068032, "dur": 80, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068116, "dur": 9, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068126, "dur": 40, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068170, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068173, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068214, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068217, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068254, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068256, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068293, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068296, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068336, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068339, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068378, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068380, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068420, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068423, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068461, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068464, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068510, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068512, "dur": 63, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068578, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068580, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068617, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068619, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068657, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068659, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068695, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068697, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068731, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068733, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068771, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068773, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068808, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068811, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068849, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068851, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068888, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068890, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068926, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068928, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068966, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149068970, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069006, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069008, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069042, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069044, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069076, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069078, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069112, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069115, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069153, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069155, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069192, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069194, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069228, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069230, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069264, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069266, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069301, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069304, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069343, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069379, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069381, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069421, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069423, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069456, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069458, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069491, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069494, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069530, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069533, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069573, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069576, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069617, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069619, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069671, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069674, "dur": 38, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069717, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069720, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069759, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069762, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069805, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069807, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069861, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069863, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069900, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069902, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069938, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069941, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149069978, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070018, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070020, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070057, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070059, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070095, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070097, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070138, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070142, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070183, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070186, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070227, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070230, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070273, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070275, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070311, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070313, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070365, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149070402, "dur": 1968, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149072375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149072378, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149072422, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149072426, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149072467, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149072469, "dur": 745, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073221, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073301, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073304, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073345, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073348, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073460, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149073498, "dur": 5120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149078624, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149078629, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149078748, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149078750, "dur": 120, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149078875, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079008, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079010, "dur": 472, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079488, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079533, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079538, "dur": 193, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079738, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079768, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079770, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079825, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079862, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149079866, "dur": 463, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080335, "dur": 144, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080483, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080486, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080532, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080535, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080649, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080691, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080694, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080735, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080774, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080777, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080819, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080822, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080861, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080864, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149080995, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081035, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081038, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081076, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081079, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081301, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081340, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081342, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081376, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081379, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081590, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081636, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081638, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081676, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081678, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081801, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081804, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081882, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081896, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081949, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081952, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149081999, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082042, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082046, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082133, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082172, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082204, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082206, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082283, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082285, "dur": 211, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082499, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082502, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082702, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082740, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082743, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082832, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082868, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082870, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082906, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082909, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082944, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082947, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082996, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149082998, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083035, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083069, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083101, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083103, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083139, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083182, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083214, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083216, "dur": 169, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083389, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083425, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083468, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083505, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083508, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083544, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083547, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083589, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083674, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083676, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083710, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083713, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083747, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083749, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083784, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083816, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083846, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083880, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083882, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083918, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083921, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083955, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083974, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149083996, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084016, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084079, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084112, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084114, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084144, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084146, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084179, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084204, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084234, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084238, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084272, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084305, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084308, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084339, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084375, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084378, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084408, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084430, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084433, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084586, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084628, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084631, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084685, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084687, "dur": 194, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084885, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084887, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084930, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149084933, "dur": 1023, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149085961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149085964, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086019, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086022, "dur": 275, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086301, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086306, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086365, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086368, "dur": 121, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086496, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086539, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149086541, "dur": 626, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149087172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149087174, "dur": 7574, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149094757, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149094764, "dur": 76, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149094845, "dur": 809, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149095658, "dur": 166476, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149262143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149262147, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149262219, "dur": 62, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149262283, "dur": 1689, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149263983, "dur": 207, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149264195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149264198, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149264251, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149264256, "dur": 2249, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149266512, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149266515, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149266577, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149266600, "dur": 50748, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149317358, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149317363, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149317466, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149317476, "dur": 1936, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319421, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319426, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319529, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319558, "dur": 125, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319686, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319688, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149319728, "dur": 554, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901149320288, "dur": 7444, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22320, "tid": 781, "ts": 1751901149353746, "dur": 1410, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901149038477, "dur": 106631, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901149145111, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901149145116, "dur": 1255, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 781, "ts": 1751901149355158, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901149014374, "dur": 315140, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901149020197, "dur": 9773, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901149329766, "dur": 7556, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901149333719, "dur": 420, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901149337442, "dur": 33, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 781, "ts": 1751901149355167, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751901149048734, "dur": 2533, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149051282, "dur": 445, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149051785, "dur": 50, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751901149051836, "dur": 364, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149053728, "dur": 2180, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901149057298, "dur": 1936, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901149062144, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901149062561, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751901149062622, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901149062768, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901149064371, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901149065568, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901149065920, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751901149066315, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751901149052233, "dur": 16171, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149068414, "dur": 251919, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149320334, "dur": 178, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149320610, "dur": 76, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149320706, "dur": 1588, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751901149052539, "dur": 15925, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149068471, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149068686, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149068760, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149069042, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149069120, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149069227, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149069400, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149069590, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149070507, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901149070599, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901149070702, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149070894, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149071223, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149071289, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901149071515, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149072014, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149072221, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149072429, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149072712, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149072937, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149073278, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149073988, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149074284, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnTriggerExit.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751901149074206, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149075144, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149075578, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Annotations\\AnnotationDisabler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751901149075406, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149076318, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149076536, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149076770, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149077017, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149077244, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149077472, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149077698, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149077911, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149078147, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149078377, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149078613, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149078827, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149079407, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149079650, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149080609, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149080876, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901149081608, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149081717, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149081799, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149082160, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901149082835, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149083028, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901149083590, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149083984, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149084118, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149084215, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149084300, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751901149084423, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149084496, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751901149084706, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149084798, "dur": 626, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751901149085425, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149085694, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149085870, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901149087020, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149087336, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149087512, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901149088213, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901149088362, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901149088911, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901149089409, "dur": 230833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149052476, "dur": 15954, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149068443, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149068643, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_FE696A2A6E4C16EE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149068895, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_034D57ECFF8286E4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149069123, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149069252, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149069362, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149069507, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_2856DC57C214A289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149069712, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_9A715A3879D71476.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149069977, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149070164, "dur": 3183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901149073460, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149073844, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149074079, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149074325, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149074604, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149074838, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149075079, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149075334, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149075553, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149075900, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\RuntimeVSUsageUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751901149075811, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149076810, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149077051, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149077272, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149077494, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149077708, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149077947, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149078187, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149078428, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149078648, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149078870, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149079114, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149079321, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149079647, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149080560, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149080773, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901149082015, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149082101, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149082182, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901149082380, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901149082969, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149083208, "dur": 1229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901149084438, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149085315, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149085414, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149085668, "dur": 1358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149087026, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149087343, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149088231, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149088808, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149088902, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149089385, "dur": 175448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901149264856, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751901149264834, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751901149265236, "dur": 2274, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751901149267514, "dur": 52755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149052519, "dur": 15928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149068454, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149068715, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149069096, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149069191, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149069401, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149069541, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_E43C745D5056CF76.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149069661, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9E61C56C19D47066.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149069852, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901149069949, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751901149070191, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751901149070334, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149070414, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901149070853, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901149070934, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901149071079, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149071270, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149071497, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149071913, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149072124, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149072332, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149072540, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149072835, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149073089, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149073830, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\INesterUnit.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751901149073617, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149074481, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149074886, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149075115, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149075363, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149075581, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149075789, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149076368, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149076600, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149076946, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149077194, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149077409, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149077640, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149077857, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149078196, "dur": 780, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\AxisControl.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751901149078098, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149079102, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149079400, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149079651, "dur": 895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149080552, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149080769, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901149081296, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149081364, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149081782, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149082053, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149082295, "dur": 1493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901149083789, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149083968, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901149084230, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901149085320, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149085415, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149085696, "dur": 1334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149087030, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149087348, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149088197, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149088801, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149088908, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901149089405, "dur": 230835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149052582, "dur": 15895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149068483, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149068557, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149068706, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149069099, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149069202, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149069573, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149069672, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149069823, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149070212, "dur": 3993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149074273, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149074490, "dur": 5052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149079646, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149079829, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149080546, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149080766, "dur": 1767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149082535, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149082615, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149082701, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149082926, "dur": 2669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149085688, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149085875, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149087334, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149087544, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149088212, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149088382, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149088907, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149089038, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149089417, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901149089552, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149090428, "dur": 92, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901149092278, "dur": 170863, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901149264840, "dur": 53453, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751901149264825, "dur": 53470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751901149318321, "dur": 1878, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751901149052636, "dur": 15862, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149068502, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149068578, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149068733, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_0197621F1ECC6D18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149069101, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_FB065B49AB03584B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149069176, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4E97288DD1B11317.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149069318, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C7DF13E3C14DE501.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149069399, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C7DF13E3C14DE501.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149069510, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_89B82555D6AF826E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149069700, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149069764, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149070005, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901149070226, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149070461, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901149070845, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901149070959, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149071123, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751901149071244, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149071504, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149071919, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149072229, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149072452, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149072706, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149072953, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149073174, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149073732, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149074020, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149074240, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149074606, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149074814, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149075042, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149075290, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149075517, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149075737, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149076312, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149076596, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149076849, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149077095, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149077310, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149077545, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149077757, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149077974, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149078274, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149078512, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149078726, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149078979, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149079334, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149079631, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149080546, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149080755, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149080864, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901149081422, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149081858, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149082084, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901149083664, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149083730, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901149084548, "dur": 903, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901149085453, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149085670, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149087027, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149087347, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149088205, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149088827, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149088888, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901149089387, "dur": 230889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149052681, "dur": 15828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149068578, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149068729, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149069043, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149069103, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149069190, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149069535, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149069635, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751901149069733, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149069966, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149070266, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149070562, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149070613, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751901149070843, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901149071213, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901149071394, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149071805, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149072021, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149072269, "dur": 2139, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\Analytics\\CoverageAnalyticsEnums.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751901149072238, "dur": 2394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149074632, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149074929, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149075195, "dur": 1130, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_6.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751901149075184, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149076542, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149076802, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149077033, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149077254, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149077487, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149077700, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149077923, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149078156, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149078402, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149078636, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149078938, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149079330, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149079645, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149081380, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149081754, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149081865, "dur": 2075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901149083941, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149084078, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149084284, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149084495, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901149084760, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901149085397, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149085677, "dur": 1347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149087025, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149087319, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149088223, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149088800, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149088913, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149089381, "dur": 58020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149148243, "dur": 271, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751901149147403, "dur": 1119, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901149148523, "dur": 171755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149052722, "dur": 15801, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149068712, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149069122, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7B05F0D16B4953B8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149069237, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149069354, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149069534, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_31C1E4D06737860D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149069645, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_B44D12B45B4CC90C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149069940, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901149070028, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901149070114, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901149070605, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751901149070824, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901149070939, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149071046, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901149071165, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751901149071266, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149071771, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149072008, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149072220, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149072590, "dur": 1178, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer\\Editor\\BoxAndWhiskerPlot.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751901149072427, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149073818, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149074057, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149074321, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149074986, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149075287, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149075516, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149075736, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149076335, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149076615, "dur": 1602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnCancelMessageListener.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751901149076565, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149078394, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149078628, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149078859, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149079319, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149079646, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149080552, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149080762, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901149081281, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149081405, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149081531, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149081832, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149082029, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149083163, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901149083947, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901149084144, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901149085395, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149085677, "dur": 1325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149087003, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149087323, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149088202, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149088832, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149088910, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149089381, "dur": 59151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901149148532, "dur": 171753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149052775, "dur": 15808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149068897, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6A35B4E7D99F65C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149069117, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_805E31AC8FEB08D0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149069216, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149069569, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149069709, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751901149069831, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901149069913, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901149070137, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149070190, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901149070285, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149070789, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149070862, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901149071166, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901149071292, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149071759, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149071984, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149072193, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149072413, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149072611, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149072829, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149073029, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149073239, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149073569, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149073913, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149074381, "dur": 746, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnTriggerStay2D.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751901149074122, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149075173, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149075433, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149075732, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149076340, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149076563, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149076820, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149077063, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149077294, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149077514, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149077737, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149077963, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149078208, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149078443, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149078662, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149078879, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149079030, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149079413, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149079650, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149080566, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149081782, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149082057, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901149082851, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149082965, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149083041, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149083301, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901149083530, "dur": 2031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901149085671, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149087031, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149087328, "dur": 872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149088200, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149088807, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149088918, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901149089387, "dur": 230883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901149326232, "dur": 1694, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 781, "ts": 1751901149355741, "dur": 2609, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 781, "ts": 1751901149358387, "dur": 3674, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 781, "ts": 1751901149348001, "dur": 15069, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}