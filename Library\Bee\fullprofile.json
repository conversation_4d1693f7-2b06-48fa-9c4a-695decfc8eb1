{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 858, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 858, "ts": **********118816, "dur": 1326, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 858, "ts": **********128841, "dur": 1682, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751901786845603, "dur": 8748, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901786854356, "dur": 60792, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901786915161, "dur": 47504, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 858, "ts": **********130533, "dur": 33, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786843448, "dur": 11480, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786854931, "dur": 250686, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786856292, "dur": 3003, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786859316, "dur": 1839, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861158, "dur": 363, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861526, "dur": 25, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861553, "dur": 64, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861620, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861623, "dur": 49, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861677, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861729, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861731, "dur": 49, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861785, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861825, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861827, "dur": 82, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861914, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861963, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786861965, "dur": 45, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862013, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862016, "dur": 40, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862058, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862060, "dur": 45, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862107, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862109, "dur": 53, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862167, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862210, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862212, "dur": 45, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862260, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862262, "dur": 45, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862309, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862311, "dur": 45, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862359, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862361, "dur": 43, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862407, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862410, "dur": 46, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862460, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862498, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862499, "dur": 46, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862549, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862550, "dur": 45, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862598, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862601, "dur": 48, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862652, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862654, "dur": 56, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862712, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862714, "dur": 43, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862760, "dur": 41, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862803, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862805, "dur": 45, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862852, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862854, "dur": 41, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862897, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862899, "dur": 39, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862941, "dur": 38, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786862982, "dur": 44, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863030, "dur": 42, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863074, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863077, "dur": 40, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863121, "dur": 36, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863159, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863160, "dur": 37, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863201, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863203, "dur": 45, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863251, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863254, "dur": 49, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863306, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863309, "dur": 55, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863368, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863372, "dur": 51, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863426, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863428, "dur": 57, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863487, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863490, "dur": 54, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863547, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863549, "dur": 49, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863600, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863602, "dur": 50, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863654, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863656, "dur": 53, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863711, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863714, "dur": 51, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863768, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863770, "dur": 52, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863825, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863827, "dur": 49, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863879, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863881, "dur": 55, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863938, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863941, "dur": 50, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863993, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786863996, "dur": 52, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864050, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864052, "dur": 45, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864100, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864102, "dur": 52, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864156, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864158, "dur": 57, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864217, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864219, "dur": 52, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864274, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864276, "dur": 44, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864323, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864325, "dur": 51, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864379, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864381, "dur": 46, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864430, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864432, "dur": 53, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864487, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864489, "dur": 46, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864538, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864540, "dur": 50, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864595, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864597, "dur": 51, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864651, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864653, "dur": 51, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864706, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864709, "dur": 47, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864758, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864760, "dur": 198, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786864962, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865016, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865018, "dur": 48, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865069, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865071, "dur": 53, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865127, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865129, "dur": 57, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865189, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865191, "dur": 53, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865248, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865251, "dur": 401, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865657, "dur": 2, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865660, "dur": 121, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865785, "dur": 5, "ph": "X", "name": "ProcessMessages 4566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865792, "dur": 53, "ph": "X", "name": "ReadAsync 4566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865848, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865849, "dur": 46, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865899, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865901, "dur": 47, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865950, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786865953, "dur": 49, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866004, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866007, "dur": 39, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866049, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866050, "dur": 47, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866100, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866104, "dur": 46, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866153, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866155, "dur": 47, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866205, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866207, "dur": 39, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866249, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866251, "dur": 44, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866298, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866300, "dur": 46, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866349, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866351, "dur": 46, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866399, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866401, "dur": 40, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866444, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866446, "dur": 46, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866496, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866498, "dur": 38, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866538, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866540, "dur": 38, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866581, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866583, "dur": 43, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866629, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866632, "dur": 42, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866677, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866679, "dur": 47, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866729, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866731, "dur": 45, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866779, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866781, "dur": 44, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866828, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786866830, "dur": 1129, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786867962, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786867965, "dur": 60, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868028, "dur": 2, "ph": "X", "name": "ProcessMessages 1186", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868031, "dur": 63, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868098, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868100, "dur": 49, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868153, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868155, "dur": 52, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868210, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868212, "dur": 54, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868268, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868270, "dur": 54, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868327, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868329, "dur": 49, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868381, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868383, "dur": 57, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868442, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868445, "dur": 49, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868496, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868498, "dur": 52, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868553, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868555, "dur": 48, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868605, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868608, "dur": 48, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868658, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868660, "dur": 53, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868715, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868717, "dur": 53, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868773, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868775, "dur": 47, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868825, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868826, "dur": 45, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868875, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868878, "dur": 48, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868930, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868932, "dur": 59, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868994, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786868997, "dur": 56, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869056, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869059, "dur": 51, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869113, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869115, "dur": 43, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869162, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869164, "dur": 39, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869206, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869208, "dur": 126, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869337, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869339, "dur": 49, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869390, "dur": 2, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869394, "dur": 46, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869443, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869445, "dur": 40, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869488, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869489, "dur": 52, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869544, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869546, "dur": 43, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869591, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869593, "dur": 37, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869633, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869653, "dur": 39, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869694, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869697, "dur": 47, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869746, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869748, "dur": 36, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869787, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869824, "dur": 36, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869861, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869863, "dur": 32, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869898, "dur": 43, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869944, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869946, "dur": 42, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869990, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786869992, "dur": 46, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870042, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870044, "dur": 45, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870092, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870093, "dur": 45, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870140, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870141, "dur": 43, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870186, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870187, "dur": 61, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870250, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870253, "dur": 50, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870305, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870308, "dur": 50, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870363, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870365, "dur": 43, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870410, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870412, "dur": 44, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870459, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870461, "dur": 46, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870509, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870512, "dur": 38, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870553, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870592, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870595, "dur": 31, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870629, "dur": 36, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870667, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870668, "dur": 33, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870705, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870734, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870737, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870788, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870790, "dur": 43, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870836, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870837, "dur": 47, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870887, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870889, "dur": 45, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870936, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870938, "dur": 46, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870987, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786870988, "dur": 46, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871038, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871040, "dur": 57, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871100, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871102, "dur": 40, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871146, "dur": 50, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871198, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871200, "dur": 43, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871245, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871247, "dur": 48, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871298, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871300, "dur": 56, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871359, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871361, "dur": 54, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871417, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871420, "dur": 62, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871485, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871488, "dur": 61, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871551, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871554, "dur": 58, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871616, "dur": 2, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871619, "dur": 49, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871670, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871671, "dur": 41, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871715, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871716, "dur": 36, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871755, "dur": 44, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871801, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871802, "dur": 41, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871846, "dur": 41, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871891, "dur": 41, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871934, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871935, "dur": 42, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786871981, "dur": 35, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872019, "dur": 47, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872069, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872071, "dur": 134, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872209, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872245, "dur": 401, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872650, "dur": 79, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872731, "dur": 9, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872742, "dur": 46, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872792, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872794, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872831, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872833, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872878, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872882, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872919, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872922, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872967, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786872970, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873006, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873009, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873048, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873051, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873087, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873090, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873140, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873143, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873212, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873215, "dur": 37, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873256, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873258, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873299, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873301, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873340, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873342, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873379, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873382, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873418, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873421, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873456, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873459, "dur": 30, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873492, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873495, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873530, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873533, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873571, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873573, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873611, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873614, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873658, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873661, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873694, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873697, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873733, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873737, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873780, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873783, "dur": 37, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873824, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873826, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873864, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873867, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873905, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873908, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873943, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873946, "dur": 37, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873986, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786873988, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874022, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874024, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874062, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874064, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874099, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874101, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874140, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874143, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874186, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874189, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874221, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874224, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874257, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874260, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874294, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874296, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786874330, "dur": 2474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786876809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786876813, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786876875, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786876878, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786876940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786876943, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786877066, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786877069, "dur": 1081, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878158, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878199, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878202, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878241, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878344, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786878382, "dur": 8899, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887289, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887293, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887393, "dur": 17, "ph": "X", "name": "ProcessMessages 1796", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887412, "dur": 416, "ph": "X", "name": "ReadAsync 1796", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887835, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887943, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786887946, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888004, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888007, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888053, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888056, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888108, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888151, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888153, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786888583, "dur": 392, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889025, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889028, "dur": 44, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889074, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889077, "dur": 360, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889559, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889563, "dur": 223, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889804, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889806, "dur": 140, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786889949, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786890000, "dur": 917, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786890926, "dur": 218, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786891149, "dur": 280, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786891477, "dur": 931, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901786892452, "dur": 164439, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********056914, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********056918, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********056991, "dur": 35, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********057029, "dur": 1727, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********058761, "dur": 495, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********059265, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********059268, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********059311, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********059316, "dur": 3704, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********063047, "dur": 17, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********063069, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********063236, "dur": 89, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********063330, "dur": 29978, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********093320, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********093327, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********093402, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********093408, "dur": 1017, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********094436, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********094440, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********094490, "dur": 29, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********094520, "dur": 885, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********095410, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********095413, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********095464, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********095514, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********095516, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********095588, "dur": 454, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": **********096047, "dur": 9377, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22320, "tid": 858, "ts": **********130568, "dur": 1188, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901786840025, "dur": 122678, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901786962707, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901786962713, "dur": 1840, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 858, "ts": **********131758, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901786818145, "dur": 289050, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901786822949, "dur": 8663, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": **********107463, "dur": 7558, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": **********111389, "dur": 143, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": **********115138, "dur": 25, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 858, "ts": **********131766, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751901786851103, "dur": 67, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901786851216, "dur": 3216, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901786854448, "dur": 420, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901786854923, "dur": 59, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751901786854982, "dur": 380, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901786856806, "dur": 2417, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901786859313, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901786860550, "dur": 1746, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901786862339, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751901786862453, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901786862845, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901786863137, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901786866442, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901786867650, "dur": 1060, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751901786869499, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901786870108, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751901786870178, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751901786855396, "dur": 17402, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901786872809, "dur": 223038, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********095849, "dur": 100, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********096047, "dur": 86, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********096147, "dur": 138, "ph": "X", "name": "EmitBuildFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********096146, "dur": 139, "ph": "X", "name": "EmitBuildFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********096285, "dur": 2129, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751901786855699, "dur": 17138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786872841, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901786872958, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901786873490, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901786874051, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786874128, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751901786874456, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786874940, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751901786874992, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786875228, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786875588, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786875831, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786876054, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786876260, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786876510, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786876726, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786876940, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786877181, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786877388, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786878035, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786878299, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786878519, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786878760, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786879062, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786879716, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786879936, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786880151, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786880767, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786880969, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786881579, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786881789, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786881991, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786882284, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786882683, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\UGUI\\UI\\PrefabLayoutRebuilder.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751901786882579, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786883321, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786883550, "dur": 1513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786885064, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901786885700, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901786886453, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786887023, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786887228, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786887538, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786888602, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786888712, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786889118, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786889400, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786889887, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786889940, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901786890379, "dur": 204950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786855687, "dur": 17135, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786872835, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901786873066, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901786873454, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1D34F993682102CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901786873687, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FAA2D6271BC6A85C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901786873977, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751901786874284, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751901786874398, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751901786874572, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751901786874734, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751901786874890, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786875041, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786875327, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786875703, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786876022, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786876229, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786876437, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786876627, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786876856, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786877060, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786877288, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786877492, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786877814, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786878250, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786878474, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786878690, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786878956, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786879751, "dur": 1346, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\AdditionHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751901786879162, "dur": 1951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786881114, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786881366, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786881575, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786881783, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786881985, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786882282, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786882860, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786883205, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786883525, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786884067, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901786884268, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901786885545, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786885974, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901786886173, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901786887584, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786888630, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786888727, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786889138, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786889398, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786889935, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901786890366, "dur": 204862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786856062, "dur": 16820, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786872952, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786873072, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786873449, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7B05F0D16B4953B8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786873520, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786873616, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_177F4FE55DE2E02C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786873985, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786874150, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901786874280, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901786874576, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751901786875029, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786875285, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786875678, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786875947, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786876170, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786876369, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786876595, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786876818, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786877025, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786877255, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786877465, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786877704, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786877918, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786878154, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786878394, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786878605, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786879038, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786879283, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786879908, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786880116, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786880865, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786881121, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786881325, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786881531, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786881730, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786881932, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786882131, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786882568, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786882844, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786883201, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786883530, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786884073, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786884259, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901786884827, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786885025, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786885189, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786885455, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901786886038, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901786886691, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786886961, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786887173, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786887461, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786887521, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786887609, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786888615, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786888707, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786889128, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786889403, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901786889531, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901786889942, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786890355, "dur": 76039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901786966395, "dur": 128981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786855834, "dur": 17015, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786872855, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786873059, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786873400, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786873617, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A44562DC2280D3AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786873821, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751901786873951, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_055043565CB01B11.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786874088, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751901786874172, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751901786874528, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751901786874785, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786874939, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751901786874998, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786875217, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786875592, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786875817, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786876398, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786876613, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786876831, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786877083, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\MissingType.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751901786877044, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786878029, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786878302, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786878519, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786878760, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786879076, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786879958, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Exceptions\\UnexpectedEnumValueException.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751901786879764, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786880573, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786880816, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786881221, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786881441, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786881659, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786881861, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786882064, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786882307, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786882862, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786883201, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786883534, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786884058, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786884248, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786884810, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786884999, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786885712, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786886364, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786886580, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786887238, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901786887575, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786887731, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786888725, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786888855, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786889402, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786889516, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786889952, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786890066, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786890370, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901786890490, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901786892346, "dur": 165305, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********059560, "dur": 34096, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": **********059546, "dur": 34114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": **********093705, "dur": 1470, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751901786855930, "dur": 16934, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786872872, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786873227, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786873449, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_9D0087D0CAC25D2C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786873537, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786873839, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751901786874720, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901786874990, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786875065, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786875319, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786875703, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786876144, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786876360, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Transitions\\FlowStateTransitionAnalyser.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901786876360, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786877317, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786877530, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786877762, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786877978, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786878209, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786878439, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786878804, "dur": 887, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Description\\IGraphDescription.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901786878650, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786879761, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786880155, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsDirectConverter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901786879977, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786880719, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786881413, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\ISettingsRepository.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901786880930, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786882244, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786882554, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786882813, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786882967, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786883300, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786883549, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786884066, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786884241, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901786884823, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786884981, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786885165, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901786885771, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786886223, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901786886385, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901786887018, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786887234, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786887547, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786888597, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786888715, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786889136, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786889405, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786889886, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786889941, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901786890355, "dur": 169215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********059594, "dur": 363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": **********059572, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": **********059970, "dur": 50, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********063584, "dur": 188, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********060061, "dur": 3803, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": **********063882, "dur": 31375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786856116, "dur": 16777, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786873376, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901786873458, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786873972, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786874092, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901786874241, "dur": 3264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901786877730, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786877983, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786878216, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786878432, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786878634, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786878953, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786879153, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786879755, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786879986, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786880657, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786880863, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786881129, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786881337, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786881563, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786881782, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786881984, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786882231, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786882452, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786882666, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786882814, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786883038, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786883319, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786883540, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786884068, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901786884256, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901786884792, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901786884969, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901786886165, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786886304, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901786886516, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901786887111, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786887208, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786887468, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786887526, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786888620, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786888709, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786889124, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786889406, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786889937, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901786890364, "dur": 204887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786856245, "dur": 16661, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786872909, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786873112, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786873451, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786873549, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786873780, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F04876DA9D7398DA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786873936, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786874406, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786874574, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786874626, "dur": 4230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786878927, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786879099, "dur": 4368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786883547, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786883656, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786884059, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786884233, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786885498, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786885612, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786885834, "dur": 1573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786887564, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786887728, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786888626, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901786888771, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901786889132, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786889417, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786889954, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786890353, "dur": 74962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786966149, "dur": 229, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751901786965317, "dur": 1069, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901786966387, "dur": 128840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786856300, "dur": 16648, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786873400, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_82A68248955BDDC4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901786873503, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874045, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874117, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874345, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874533, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874748, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874832, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874928, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901786874980, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786875209, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786875794, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786876036, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786876303, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786876546, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786876776, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786877090, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IVariableUnit.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751901786876999, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786877964, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786878276, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Products\\Product.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751901786878209, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786879034, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786879239, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786879945, "dur": 711, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.NullableValueTypes.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751901786879929, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786880843, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786881238, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786881485, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786881706, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786881916, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786882398, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786882695, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\UGUI\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751901786882690, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786883212, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786883549, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786884064, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901786884263, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901786884866, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901786885081, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901786885643, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901786885830, "dur": 937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786886772, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901786887534, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786888607, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786888745, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786889141, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786889404, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786889958, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901786890372, "dur": 205017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********103251, "dur": 1536, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 858, "ts": **********132328, "dur": 2421, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 858, "ts": **********134795, "dur": 3217, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 858, "ts": **********125302, "dur": 14109, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}