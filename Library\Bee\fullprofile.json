{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 852, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 852, "ts": 1751901744257855, "dur": 1373, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 852, "ts": 1751901744267374, "dur": 1872, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751901744011684, "dur": 6184, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901744017874, "dur": 60576, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901744078460, "dur": 44701, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 852, "ts": 1751901744269253, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744009152, "dur": 10488, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744019643, "dur": 222386, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744020696, "dur": 3184, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744023888, "dur": 1737, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744025630, "dur": 358, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744025992, "dur": 19, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026013, "dur": 50, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026066, "dur": 2, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026069, "dur": 53, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026125, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026127, "dur": 44, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026173, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026177, "dur": 112, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026293, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026382, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026385, "dur": 52, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026440, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026442, "dur": 43, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026487, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026490, "dur": 56, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026549, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026551, "dur": 51, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026605, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026607, "dur": 53, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026663, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026666, "dur": 55, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026723, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026726, "dur": 57, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026787, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026789, "dur": 52, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026844, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026847, "dur": 46, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026897, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026899, "dur": 82, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026985, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744026988, "dur": 51, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027042, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027045, "dur": 59, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027109, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027112, "dur": 56, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027171, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027174, "dur": 159, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027335, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027338, "dur": 51, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027392, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027395, "dur": 48, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027447, "dur": 46, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027495, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027497, "dur": 36, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027535, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027537, "dur": 41, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027580, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027582, "dur": 44, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027630, "dur": 51, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027683, "dur": 44, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027729, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027731, "dur": 40, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027775, "dur": 40, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027817, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027819, "dur": 43, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027864, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027866, "dur": 45, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027914, "dur": 49, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744027966, "dur": 43, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028010, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028012, "dur": 44, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028058, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028059, "dur": 43, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028105, "dur": 37, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028145, "dur": 44, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028192, "dur": 42, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028237, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028238, "dur": 86, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028327, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028330, "dur": 47, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028380, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028383, "dur": 45, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028431, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028433, "dur": 52, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028489, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028539, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028540, "dur": 44, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028586, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028588, "dur": 42, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028637, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028639, "dur": 42, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028685, "dur": 42, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028729, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028730, "dur": 45, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028777, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028780, "dur": 41, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028824, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028826, "dur": 40, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028868, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028870, "dur": 45, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028918, "dur": 44, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028964, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744028966, "dur": 43, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029011, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029013, "dur": 40, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029055, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029056, "dur": 40, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029099, "dur": 44, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029147, "dur": 41, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029190, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029191, "dur": 42, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029235, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029237, "dur": 42, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029281, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029284, "dur": 38, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029325, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029329, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029377, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029379, "dur": 51, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029433, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029435, "dur": 48, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029486, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029488, "dur": 46, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029535, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029537, "dur": 39, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029578, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029580, "dur": 211, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029794, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029843, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029845, "dur": 40, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029889, "dur": 46, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029936, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029938, "dur": 42, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029982, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744029983, "dur": 63, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030051, "dur": 73, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030127, "dur": 2, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030130, "dur": 60, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030194, "dur": 2, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030197, "dur": 46, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030246, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030248, "dur": 42, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030293, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030295, "dur": 56, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030354, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030356, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030416, "dur": 2, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030419, "dur": 44, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030466, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030469, "dur": 41, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030514, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030516, "dur": 48, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030567, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030570, "dur": 48, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030621, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030623, "dur": 51, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030677, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030680, "dur": 49, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030731, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030733, "dur": 46, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030782, "dur": 3, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030785, "dur": 49, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030837, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030839, "dur": 47, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030889, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030891, "dur": 37, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030931, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030933, "dur": 49, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030985, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744030987, "dur": 56, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031046, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031050, "dur": 50, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031103, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031106, "dur": 54, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031163, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031165, "dur": 86, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031254, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031258, "dur": 72, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031333, "dur": 2, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031338, "dur": 41, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031381, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031383, "dur": 51, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031437, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031440, "dur": 49, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031493, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031496, "dur": 42, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031541, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031543, "dur": 34, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031581, "dur": 31, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031614, "dur": 8, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031623, "dur": 38, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031664, "dur": 31, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031698, "dur": 35, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031736, "dur": 37, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031776, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031779, "dur": 47, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031829, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031831, "dur": 39, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031874, "dur": 32, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031910, "dur": 32, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031945, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031986, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744031987, "dur": 32, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032022, "dur": 31, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032056, "dur": 32, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032090, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032092, "dur": 41, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032136, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032138, "dur": 38, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032180, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032182, "dur": 34, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032219, "dur": 32, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032254, "dur": 36, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032293, "dur": 33, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032329, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032331, "dur": 41, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032375, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032377, "dur": 52, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032431, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032433, "dur": 46, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032481, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032483, "dur": 51, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032536, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032538, "dur": 37, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032578, "dur": 38, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032618, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032621, "dur": 39, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032662, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032665, "dur": 53, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032720, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032722, "dur": 46, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032769, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032771, "dur": 46, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032820, "dur": 45, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032867, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032868, "dur": 46, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032917, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032920, "dur": 52, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032974, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744032976, "dur": 42, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033020, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033022, "dur": 47, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033071, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033072, "dur": 44, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033118, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033120, "dur": 43, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033164, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033165, "dur": 39, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033207, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033209, "dur": 50, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033261, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033263, "dur": 47, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033313, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033315, "dur": 50, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033366, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033369, "dur": 46, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033417, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033420, "dur": 39, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033462, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033464, "dur": 55, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033520, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033522, "dur": 48, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033572, "dur": 8, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033581, "dur": 50, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033632, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033634, "dur": 44, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033679, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033682, "dur": 41, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033725, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033728, "dur": 47, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033780, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033782, "dur": 38, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033823, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033824, "dur": 39, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033865, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033867, "dur": 38, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033908, "dur": 32, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033944, "dur": 31, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744033978, "dur": 27, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034008, "dur": 35, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034045, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034055, "dur": 40, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034097, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034098, "dur": 34, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034136, "dur": 78, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034219, "dur": 421, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034644, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034647, "dur": 106, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034757, "dur": 6, "ph": "X", "name": "ProcessMessages 4971", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034765, "dur": 59, "ph": "X", "name": "ReadAsync 4971", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034827, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034830, "dur": 59, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034893, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034896, "dur": 43, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034942, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034944, "dur": 49, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034996, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744034999, "dur": 40, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035041, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035043, "dur": 46, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035092, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035095, "dur": 43, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035140, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035142, "dur": 42, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035186, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035189, "dur": 51, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035244, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035247, "dur": 59, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035309, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035311, "dur": 57, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035372, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035374, "dur": 57, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035434, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035437, "dur": 44, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035483, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035485, "dur": 41, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035529, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035531, "dur": 42, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035575, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035578, "dur": 46, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035627, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035629, "dur": 46, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035679, "dur": 39, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035719, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035721, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035759, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035761, "dur": 37, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035802, "dur": 31, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035835, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035836, "dur": 39, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035879, "dur": 29, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035911, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744035913, "dur": 139, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036057, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036110, "dur": 426, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036540, "dur": 95, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036639, "dur": 10, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036651, "dur": 38, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036692, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036695, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036734, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036737, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036781, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036784, "dur": 36, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036823, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036827, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036873, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036875, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036920, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036922, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036953, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036955, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036988, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744036992, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037030, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037033, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037071, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037074, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037117, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037120, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037171, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037174, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037211, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037214, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037253, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037256, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037290, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037291, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037330, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037332, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037375, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037378, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037412, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037414, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037452, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037454, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037489, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037491, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037526, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037529, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037567, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037570, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037606, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037608, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037642, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037644, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037683, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037686, "dur": 35, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037723, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037725, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037762, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037765, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037803, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037806, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037847, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037849, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037888, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037891, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037929, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037932, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037970, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744037973, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038009, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038011, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038044, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038046, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038081, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038085, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038119, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038121, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038159, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038161, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038196, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038198, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038234, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038236, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038268, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038271, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038503, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038542, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744038545, "dur": 2638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041191, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041236, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041240, "dur": 134, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041380, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041415, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041417, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041658, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041694, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041697, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041737, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041774, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744041776, "dur": 4428, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046211, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046215, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046262, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046265, "dur": 188, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046458, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046505, "dur": 482, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744046994, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047032, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047034, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047072, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047075, "dur": 149, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047230, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047265, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047268, "dur": 60, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047333, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047364, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047375, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047408, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047410, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047782, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744047816, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048006, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048009, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048045, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048048, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048075, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048078, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048166, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048202, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048206, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048236, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048238, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048315, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048345, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048347, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048378, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048380, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048455, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048490, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048491, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048522, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048524, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048649, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048679, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048681, "dur": 113, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048799, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048833, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048880, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048911, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048979, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744048981, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049012, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049090, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049121, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049123, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049187, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049209, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049296, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049329, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049331, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049369, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049371, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049402, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049404, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049439, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049486, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049526, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049528, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049564, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049566, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049606, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049609, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049711, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049742, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049744, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049776, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049779, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049814, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049816, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049855, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049858, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049889, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049891, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049925, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744049929, "dur": 99, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050031, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050034, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050072, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050122, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050163, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050165, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050201, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050204, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050240, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050243, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050279, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050281, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050321, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050323, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050363, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050365, "dur": 588, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050957, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744050959, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744051021, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744051026, "dur": 895, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744051925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744051928, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744051996, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744051999, "dur": 467, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744052469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744052471, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744052519, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744052522, "dur": 132, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744059218, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744059228, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744059334, "dur": 773, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744060112, "dur": 137121, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744197242, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744197246, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744197307, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744197332, "dur": 51, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744197387, "dur": 2022, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744199418, "dur": 470, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744199892, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744199899, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744199947, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744199952, "dur": 2200, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744202159, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744202162, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744202219, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744202247, "dur": 30386, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744232642, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744232646, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744232714, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744232720, "dur": 1162, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744233891, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744233895, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744233966, "dur": 24, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744233992, "dur": 319, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744234316, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744234319, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744234363, "dur": 541, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901744234909, "dur": 6863, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22320, "tid": 852, "ts": 1751901744269275, "dur": 2126, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901744005971, "dur": 117275, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901744123248, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901744123253, "dur": 1322, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 852, "ts": 1751901744271409, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901743982502, "dur": 261275, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901743987464, "dur": 9430, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901744244099, "dur": 7997, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901744249351, "dur": 162, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901744252266, "dur": 27, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 852, "ts": 1751901744271531, "dur": 26, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751901744016147, "dur": 2526, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744018694, "dur": 514, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744019267, "dur": 50, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751901744019318, "dur": 388, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744021227, "dur": 2678, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901744025445, "dur": 1325, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901744027093, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901744027710, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901744027973, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901744028145, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751901744028383, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751901744028663, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901744029186, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901744032063, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751901744034911, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901744035078, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751901744035457, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751901744019750, "dur": 16911, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744036672, "dur": 197994, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744234667, "dur": 136, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744234803, "dur": 116, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744235005, "dur": 59, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744235084, "dur": 1469, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751901744020105, "dur": 16584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744036702, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744037298, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744037358, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744037524, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1974DA72EEA9484D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744037693, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B1D6EB5640F05713.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744037988, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901744038136, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901744038269, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751901744038500, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751901744038984, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744039199, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744039654, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744039893, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744040409, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744040634, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744040874, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744041084, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744041298, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744041516, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744041765, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744042007, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744042231, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744043151, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744043850, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744044067, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744044285, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744044503, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744044858, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744045064, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744045286, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744045508, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744045724, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744045932, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744046135, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744046354, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744046674, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744046992, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744047829, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744048014, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744048616, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744048955, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744049587, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744049769, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744050251, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744050350, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744050639, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744050714, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744051567, "dur": 1100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744052690, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901744053155, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744053285, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744053916, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744054037, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744054476, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744054592, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744054890, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901744054994, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744056722, "dur": 141260, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901744200304, "dur": 33056, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751901744200287, "dur": 33076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751901744233384, "dur": 1239, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751901744020256, "dur": 16475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901744036735, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744037337, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744037696, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751901744037959, "dur": 3952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744041986, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744042154, "dur": 4774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744047024, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744047204, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744047824, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744048010, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744049463, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744049670, "dur": 1798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744051556, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744051727, "dur": 1333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744053150, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901744053331, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901744053802, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901744053929, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901744054473, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901744054877, "dur": 71445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901744126323, "dur": 108365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744020174, "dur": 16534, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744036715, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901744036879, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744037340, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901744037834, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_055043565CB01B11.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901744038324, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744038663, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901744038981, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744039199, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744039630, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744039870, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744040079, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744040300, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744040864, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744041086, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744041313, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744041534, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744041786, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744042003, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744042299, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744042707, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744042924, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744043150, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744043850, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744044089, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744044299, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744044512, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744044927, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744045141, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744045363, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744045619, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744045846, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744046045, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744046287, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744046512, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744046671, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744046990, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744047810, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744047871, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901744048105, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744048178, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901744048972, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901744049153, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901744049894, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901744050853, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744050989, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744051169, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744051289, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744051406, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744051575, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744052696, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744053166, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744053789, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744053899, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744054460, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901744054900, "dur": 179767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744020240, "dur": 16480, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744036724, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744036932, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744037106, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6A35B4E7D99F65C8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744037334, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744037472, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744037613, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744037693, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751901744037805, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744037936, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744038163, "dur": 4228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901744042439, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744042560, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744042801, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744043022, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744043728, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744043952, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744044192, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744044415, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744044632, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744044849, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744045048, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744045271, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744045495, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744045706, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744045930, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744046138, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744046295, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744046671, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744047013, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744047833, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744048058, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744048120, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901744048801, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744048944, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901744049151, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744049243, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901744049437, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901744050808, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751901744050994, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744051071, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744051164, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744051288, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744051392, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744051548, "dur": 1146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744052694, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744053193, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744053800, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744053922, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744054389, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744054456, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744054875, "dur": 70488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744126102, "dur": 177, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1751901744125364, "dur": 921, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901744126285, "dur": 108378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744020303, "dur": 16434, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744036743, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744036830, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744036952, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744037256, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_FB065B49AB03584B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744037565, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744037785, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744037952, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901744038025, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901744038183, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751901744038614, "dur": 667, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751901744039282, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744039827, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744040118, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744040337, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744040578, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744040832, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744041074, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744041293, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744041510, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744041752, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744042070, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744042295, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744043249, "dur": 1312, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\RuntimeCodebase.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901744043249, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744044769, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744044976, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744045184, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744045431, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744045660, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744045882, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744046136, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744046346, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744046723, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744046988, "dur": 1599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744048588, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744048782, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901744050218, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901744050478, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744050605, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901744051304, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744051409, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744051569, "dur": 1096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744052695, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744053167, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744053807, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744053928, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744054393, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744054456, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901744054903, "dur": 179780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744020364, "dur": 16385, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744037105, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744037558, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_91801C7D6E952EB7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901744037633, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F04876DA9D7398DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038052, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038366, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038464, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038564, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744038663, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038733, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038844, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751901744038995, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744039344, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744039761, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744039968, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744040197, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744040414, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744040653, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744041150, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744041373, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744041650, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Control\\Cache.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751901744041597, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744042648, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744042858, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744043074, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744043770, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744043972, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744044180, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744044412, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744044678, "dur": 1998, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\CoverageSession.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751901744044626, "dur": 2172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744046798, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744046995, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744047832, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901744048037, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901744049003, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901744049230, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744049304, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901744050069, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744050279, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744050363, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901744050628, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901744051296, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744051387, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744051560, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901744051702, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901744052686, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744053171, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744053804, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744053909, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744054392, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744054481, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901744054893, "dur": 179777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744020416, "dur": 16338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744036758, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901744037119, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2D31BD0D6ECE4F8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901744037221, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744037345, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744038038, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751901744038478, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751901744038615, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901744039027, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744039539, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744039804, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744040023, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744040237, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744040487, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744040703, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744041122, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744041360, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744041579, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744041817, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Windows\\AboutWindow\\IAboutable.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751901744041806, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744042654, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744042867, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744043230, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Analytics\\MigrationAnalytics.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751901744043070, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744043798, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744044008, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744044210, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744044428, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744044852, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744045076, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744045288, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744045530, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744045777, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744045993, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744046231, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744046460, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744046760, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744047025, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744047827, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901744048033, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901744048644, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901744048845, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901744049023, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901744051401, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744051568, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744052700, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744053171, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744053826, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744053919, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901744054056, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901744054418, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744054479, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901744054901, "dur": 179764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744020455, "dur": 16307, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744036985, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F9300E41473E6FF2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901744037340, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744038038, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901744038208, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751901744038543, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751901744038712, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901744038975, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744039191, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744039761, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744039987, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744040222, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744040429, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744040653, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744041009, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744041227, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744041451, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744041665, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744041939, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744042176, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744042415, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744042791, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744042999, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744043221, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744043900, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744044125, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744044350, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744044570, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744044830, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744045031, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744045258, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744045535, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744045760, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744045966, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744046218, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744046400, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744046747, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744046993, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744047830, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901744048025, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901744048566, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744048646, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901744048837, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901744049103, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901744049884, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744049973, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901744050893, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744051135, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744051234, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744051396, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744051562, "dur": 1129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744052691, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744053165, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744053795, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744053930, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744054394, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744054455, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744054880, "dur": 145444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901744200343, "dur": 289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751901744200325, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751901744200669, "dur": 2241, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751901744202919, "dur": 31753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901744240928, "dur": 1173, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 852, "ts": 1751901744272546, "dur": 2421, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 852, "ts": 1751901744275006, "dur": 3005, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 852, "ts": 1751901744265017, "dur": 16250, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}