{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 799, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 799, "ts": 1751901387376787, "dur": 1146, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 799, "ts": 1751901387386282, "dur": 2931, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751901387088014, "dur": 14047, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901387102065, "dur": 50226, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751901387152301, "dur": 37895, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 799, "ts": 1751901387389223, "dur": 21, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387085745, "dur": 9978, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387095725, "dur": 265719, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387101817, "dur": 3089, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387104916, "dur": 1697, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387106618, "dur": 308, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387106930, "dur": 15, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387106947, "dur": 59, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107010, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107013, "dur": 52, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107068, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107070, "dur": 38, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107110, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107112, "dur": 69, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107185, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107226, "dur": 35, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107263, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107265, "dur": 31, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107298, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107300, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107337, "dur": 33, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107374, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107408, "dur": 33, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107444, "dur": 33, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107481, "dur": 34, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107518, "dur": 35, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107556, "dur": 33, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107591, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107593, "dur": 31, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107627, "dur": 30, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107660, "dur": 35, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107697, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107698, "dur": 36, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107738, "dur": 34, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107775, "dur": 34, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107813, "dur": 34, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107850, "dur": 71, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107926, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107991, "dur": 2, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387107995, "dur": 51, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108049, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108051, "dur": 37, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108090, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108092, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108127, "dur": 36, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108165, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108166, "dur": 35, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108205, "dur": 34, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108241, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108244, "dur": 37, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108284, "dur": 30, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108319, "dur": 33, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108355, "dur": 37, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108395, "dur": 34, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108432, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108433, "dur": 42, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108479, "dur": 32, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108513, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108515, "dur": 44, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108563, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108565, "dur": 50, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108618, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108621, "dur": 49, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108673, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108675, "dur": 41, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108718, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108720, "dur": 34, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108756, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108758, "dur": 93, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387108855, "dur": 443, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109304, "dur": 3, "ph": "X", "name": "ProcessMessages 2028", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109309, "dur": 108, "ph": "X", "name": "ReadAsync 2028", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109419, "dur": 6, "ph": "X", "name": "ProcessMessages 5110", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109427, "dur": 43, "ph": "X", "name": "ReadAsync 5110", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109475, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109478, "dur": 51, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109531, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109533, "dur": 43, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109580, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109582, "dur": 40, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109626, "dur": 50, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109679, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109681, "dur": 47, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109730, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109733, "dur": 45, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109781, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109783, "dur": 40, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109826, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387109828, "dur": 277, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110109, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110163, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110166, "dur": 146, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110317, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110319, "dur": 57, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110378, "dur": 1, "ph": "X", "name": "ProcessMessages 1657", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110381, "dur": 46, "ph": "X", "name": "ReadAsync 1657", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110429, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110431, "dur": 42, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110475, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110477, "dur": 33, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110511, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110513, "dur": 68, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110583, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110585, "dur": 41, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110629, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110631, "dur": 50, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110685, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110726, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110727, "dur": 34, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110764, "dur": 33, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110801, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110830, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110867, "dur": 33, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110904, "dur": 63, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387110971, "dur": 45, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111019, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111021, "dur": 46, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111069, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111071, "dur": 31, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111106, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111144, "dur": 30, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111177, "dur": 43, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111223, "dur": 29, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111253, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111255, "dur": 37, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111295, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111297, "dur": 42, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111343, "dur": 43, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111389, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111391, "dur": 40, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111433, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111435, "dur": 40, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111477, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111479, "dur": 38, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111520, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111521, "dur": 38, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111564, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111566, "dur": 49, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111617, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111619, "dur": 45, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111667, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111670, "dur": 48, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111720, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111722, "dur": 45, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111770, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111772, "dur": 50, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111824, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111826, "dur": 39, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111867, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111869, "dur": 39, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111910, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111911, "dur": 30, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111944, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111946, "dur": 49, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387111998, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112001, "dur": 43, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112046, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112047, "dur": 39, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112089, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112090, "dur": 35, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112128, "dur": 35, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112167, "dur": 41, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112210, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112211, "dur": 49, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112262, "dur": 2, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112265, "dur": 44, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112310, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112312, "dur": 42, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112357, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112358, "dur": 39, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112399, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112401, "dur": 41, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112446, "dur": 44, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112491, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112493, "dur": 44, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112539, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112540, "dur": 43, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112586, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112587, "dur": 70, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112660, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112662, "dur": 64, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112729, "dur": 2, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112732, "dur": 55, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112791, "dur": 2, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112794, "dur": 44, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112840, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112843, "dur": 44, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112889, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112891, "dur": 52, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112946, "dur": 3, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387112951, "dur": 54, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113008, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113011, "dur": 47, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113069, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113072, "dur": 47, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113121, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113124, "dur": 53, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113180, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113182, "dur": 46, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113231, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113233, "dur": 40, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113276, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113278, "dur": 40, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113321, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113324, "dur": 45, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113372, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113374, "dur": 46, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113422, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113425, "dur": 46, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113474, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113477, "dur": 41, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113521, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113523, "dur": 43, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113569, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113571, "dur": 44, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113617, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113620, "dur": 46, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113668, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113671, "dur": 49, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113724, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113727, "dur": 43, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113773, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113775, "dur": 43, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113823, "dur": 38, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113863, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113864, "dur": 39, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113907, "dur": 51, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113960, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387113962, "dur": 58, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114023, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114025, "dur": 47, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114073, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114075, "dur": 43, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114119, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114121, "dur": 45, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114168, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114170, "dur": 42, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114216, "dur": 41, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114260, "dur": 42, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114304, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114306, "dur": 37, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114345, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114347, "dur": 41, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114391, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114392, "dur": 36, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114431, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114433, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114472, "dur": 35, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114511, "dur": 35, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114548, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114550, "dur": 35, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114588, "dur": 28, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114619, "dur": 36, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114659, "dur": 35, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114695, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114697, "dur": 33, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114732, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114734, "dur": 31, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114768, "dur": 38, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114808, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114811, "dur": 40, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114853, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114855, "dur": 42, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114901, "dur": 38, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114941, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114944, "dur": 43, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114991, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387114993, "dur": 43, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115039, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115041, "dur": 44, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115088, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115089, "dur": 37, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115129, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115132, "dur": 45, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115179, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115182, "dur": 34, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115218, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115219, "dur": 35, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115258, "dur": 33, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115293, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115294, "dur": 33, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115331, "dur": 35, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115368, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115370, "dur": 33, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115405, "dur": 32, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115441, "dur": 42, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115487, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115489, "dur": 52, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115543, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115545, "dur": 37, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115584, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115585, "dur": 33, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115620, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115622, "dur": 34, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115667, "dur": 32, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115700, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115702, "dur": 40, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115746, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115749, "dur": 45, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115797, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115799, "dur": 37, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115838, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115841, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115880, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115881, "dur": 68, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115952, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387115954, "dur": 42, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116000, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116002, "dur": 46, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116052, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116054, "dur": 173, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116231, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116233, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116273, "dur": 350, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116627, "dur": 79, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116710, "dur": 8, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116720, "dur": 41, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116765, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116768, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116806, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116809, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116853, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116856, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116911, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116942, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116946, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116986, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387116988, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117029, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117031, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117071, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117073, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117118, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117120, "dur": 100, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117224, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117226, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117271, "dur": 3, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117275, "dur": 39, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117317, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117319, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117356, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117360, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117398, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117400, "dur": 40, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117444, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117447, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117484, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117486, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117530, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117563, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117565, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117601, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117604, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117646, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117647, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117680, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117683, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117714, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117717, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117748, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117780, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117782, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117813, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117815, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117848, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117850, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117884, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117927, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117929, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117967, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387117969, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118004, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118007, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118040, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118042, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118083, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118086, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118122, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118125, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118172, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118175, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118213, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118216, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118251, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118253, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118288, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118291, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118329, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118331, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118373, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118376, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118414, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118416, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118452, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118455, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118492, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118494, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118535, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118538, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118574, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118576, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118616, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118619, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118658, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118661, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118697, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118699, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118733, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118736, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118770, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118773, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118814, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118816, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118854, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118856, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118891, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118893, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118932, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118934, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118969, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387118972, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119005, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119028, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119062, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119064, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119099, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119101, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119134, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119136, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119169, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119583, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119620, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387119622, "dur": 1679, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121307, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121348, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121352, "dur": 199, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121554, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121557, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121610, "dur": 351, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387121967, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122008, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122010, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122082, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122125, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122127, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122162, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122165, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122302, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122335, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387122337, "dur": 5174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387127519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387127523, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387127577, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387127580, "dur": 79, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387127663, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387127699, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128035, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128076, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128079, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128115, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128117, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128271, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128320, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128322, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128355, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128357, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128387, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128419, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128421, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128451, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128453, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128555, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128586, "dur": 296, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128886, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128922, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128925, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387128995, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129029, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129031, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129081, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129116, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129119, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129149, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129188, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129190, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129295, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129330, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129360, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129363, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129440, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129480, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129482, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129511, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129513, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129770, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129804, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129806, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129838, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129840, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129874, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387129876, "dur": 141, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130023, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130065, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130102, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130104, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130136, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130138, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130169, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130171, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130209, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130211, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130244, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130246, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130278, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130375, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130410, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130413, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130451, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130486, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130523, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130562, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130669, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130672, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130710, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130983, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387130993, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131024, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131026, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131056, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131105, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131108, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131142, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131144, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131178, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131181, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131215, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131217, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131250, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131284, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131287, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131324, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131327, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131364, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131367, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131401, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131403, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131435, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131438, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131478, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131480, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131515, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131517, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131548, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131550, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131597, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131600, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131676, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131728, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131729, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131779, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387131783, "dur": 464, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132254, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132307, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132310, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132347, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132432, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132463, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387132493, "dur": 841, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133340, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133390, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133392, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133430, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133432, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133805, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133841, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133885, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387133887, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134038, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134071, "dur": 550, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134634, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134636, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134678, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134680, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134735, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134737, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134838, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134885, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134888, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387134934, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135029, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135077, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135080, "dur": 296, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135381, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135383, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135422, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135425, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135487, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135488, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135611, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135650, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135652, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135695, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135697, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135759, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387135803, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387136057, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387136099, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387136102, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387136134, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387136201, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387136230, "dur": 1093, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387137330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387137332, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387137377, "dur": 762, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387138143, "dur": 164613, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387302765, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387302772, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387302831, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387302855, "dur": 39, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387302896, "dur": 3257, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387306160, "dur": 949, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387307116, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387307119, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387307165, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387307170, "dur": 2426, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387309604, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387309608, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387309664, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387309695, "dur": 39896, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387349601, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387349605, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387349649, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387349654, "dur": 922, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387350582, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387350585, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387350629, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387350655, "dur": 658, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387351318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387351320, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387351392, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387351394, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387351431, "dur": 500, "ph": "X", "name": "ProcessMessages 5", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751901387351936, "dur": 9200, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 22320, "tid": 799, "ts": 1751901387389246, "dur": 1720, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901387082114, "dur": 108140, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901387190258, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751901387190265, "dur": 1428, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 799, "ts": 1751901387390970, "dur": 21, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901387054809, "dur": 308441, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901387064088, "dur": 9581, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901387364000, "dur": 8388, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901387368638, "dur": 163, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751901387372542, "dur": 26, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 799, "ts": 1751901387390993, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751901387092579, "dur": 2661, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387095259, "dur": 453, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387095773, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751901387095826, "dur": 386, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387097674, "dur": 7382, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901387106318, "dur": 1532, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751901387110247, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751901387111812, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751901387116823, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751901387096258, "dur": 20683, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387116951, "dur": 234652, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387351604, "dur": 104, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387351721, "dur": 167, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387351897, "dur": 119, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387352118, "dur": 75, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387352205, "dur": 96, "ph": "X", "name": "EmitBuildFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387352204, "dur": 97, "ph": "X", "name": "EmitBuildFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387352302, "dur": 1861, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751901387096548, "dur": 20426, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387116986, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387117233, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387117569, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387117730, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4E97288DD1B11317.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387117889, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387118323, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387118533, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387118843, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387119014, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387119280, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387119487, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387119545, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901387119910, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751901387120007, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387120079, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387120574, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387120788, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387121061, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387121304, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387121560, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387121794, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387122017, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387122261, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387122355, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387122648, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387123108, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387123685, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387124048, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387124271, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387124946, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387125190, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387125417, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387125641, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387125891, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387126182, "dur": 1474, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\Views\\Selectors.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751901387126108, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387127820, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387128299, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387128454, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387129013, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387129218, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901387129828, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387130030, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387130091, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751901387130281, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901387130781, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751901387132364, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387132496, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387133185, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387134290, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387134755, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387135593, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387135785, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387136368, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387136541, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751901387137008, "dur": 214539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387096665, "dur": 20335, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387117005, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387117437, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387117616, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387117749, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387117957, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387118195, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751901387118429, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751901387118539, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751901387118698, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387118768, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387118820, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751901387119129, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751901387119540, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751901387119633, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387120019, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387120277, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387120746, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387121021, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387121257, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387121466, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387121710, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387121949, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387122173, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387122667, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387123052, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387123658, "dur": 1097, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_2_2_to_1_2_3.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751901387123355, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387124805, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387125097, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387125347, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387125582, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387125786, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387126048, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387126279, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387126499, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387126694, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387126905, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387127121, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387127405, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387127608, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387128043, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387128188, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387128434, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387129004, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387129196, "dur": 1388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901387130585, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387130696, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387130764, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387131150, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751901387132106, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387132206, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751901387132288, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751901387132491, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387133213, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387134286, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387134754, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387135580, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387135768, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387136349, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387136536, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387136986, "dur": 170462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751901387307478, "dur": 499, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751901387307450, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751901387308034, "dur": 2480, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751901387310519, "dur": 41319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387096640, "dur": 20348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387116994, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387117247, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387117481, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387117732, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387118031, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387118253, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387118609, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901387118879, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387118951, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901387119014, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387119088, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901387119237, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751901387119341, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387119404, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751901387119547, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901387119728, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751901387119997, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387120212, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387120629, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387120843, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387121044, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387121293, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387121529, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Plugin\\BoltStateConfiguration.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751901387121518, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387122363, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387122601, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387122826, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387123212, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387123467, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387123768, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387123988, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387124202, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387124806, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387125060, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnEndDragMessageListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751901387125059, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387126085, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387126456, "dur": 1202, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Analytics\\InputActionsEditorSessionAnalytic.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751901387126456, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387127967, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387128189, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387128429, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387129005, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387129185, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387129330, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901387130993, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387131167, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751901387131451, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751901387132256, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387132428, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751901387132484, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387132637, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387133184, "dur": 1104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387134289, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387134749, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387135582, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387135799, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387136337, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387136537, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387136981, "dur": 55637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387193423, "dur": 205, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1751901387192619, "dur": 1017, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751901387193636, "dur": 158323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387096752, "dur": 20260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387117016, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387117240, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_4DE8BB45CF6CF9CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387117609, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387117734, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387117880, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387118059, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_83F4E4F0FA017AFD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387118233, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751901387118316, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387118427, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387118547, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387118696, "dur": 3481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387122266, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387122509, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387123228, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387123464, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387123982, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387124203, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387124819, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387125152, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387125386, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387125624, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387125850, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387126072, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387126305, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387126525, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387126739, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387126949, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387127159, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387127370, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387127640, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\AssetMenu\\AssetsSelection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751901387127580, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387128360, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387128443, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387129002, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387129268, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387129342, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387129838, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387129935, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387130265, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387130963, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387132066, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387132137, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387132366, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387132474, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387132651, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387133192, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387134274, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751901387134784, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387134958, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387135793, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387135970, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387136557, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387136677, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387137002, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751901387137122, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387139010, "dur": 164672, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751901387307451, "dur": 43000, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751901387307436, "dur": 43017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751901387350481, "dur": 1012, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751901387096887, "dur": 20131, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387117024, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387117113, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387117175, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_6BC1B44B0B58CEF0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387117605, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387117761, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387118403, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901387118508, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901387118828, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751901387119153, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901387119358, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387119525, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901387120167, "dur": 80, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751901387120247, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387120501, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387120730, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387120983, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_EditorCoroutine.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901387120932, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387121707, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387121923, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387122171, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387122397, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387122607, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387122819, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387123300, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387123574, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387123811, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387124029, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387124247, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387124845, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387125157, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387125395, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387125631, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387126306, "dur": 929, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\InternedString.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751901387125873, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387127332, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387127537, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387127777, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387128307, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387128459, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387129206, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387129477, "dur": 1255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901387130777, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387130952, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751901387131021, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901387131942, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751901387132650, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387133210, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387134302, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387134750, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387135588, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387135764, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387136356, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387136564, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387136983, "dur": 56661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751901387193644, "dur": 157884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387096988, "dur": 20042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387117037, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387117149, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387117257, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387117447, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2D31BD0D6ECE4F8B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387117605, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387117733, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387118084, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_DC466A201EF621B1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387118312, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387118515, "dur": 4323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387122921, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387123228, "dur": 5142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387128451, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387128583, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387128999, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387129164, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387129289, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387130774, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387130993, "dur": 2115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387133208, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387133394, "dur": 1263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387134782, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387134954, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387135591, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387135797, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751901387135937, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751901387136361, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387136554, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751901387137015, "dur": 214951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387097037, "dur": 20009, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387117051, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387117152, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387117236, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387117481, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_034D57ECFF8286E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387117743, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387117989, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BF2CDC1257CA045F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387118329, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387118467, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901387118517, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387119006, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387119138, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901387119414, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751901387119788, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387119895, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751901387119980, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387120277, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387120719, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387120932, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387121152, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387121377, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387121631, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387121853, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387122074, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387122307, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387122529, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387122763, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387123032, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387123272, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387123877, "dur": 885, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\LudiqGUIUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751901387123682, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387124929, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387125190, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387125415, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387125639, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387125879, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387126264, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387126566, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387126779, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387127054, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387127295, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387127489, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387127761, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387128253, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387128438, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387129006, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387129194, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901387129936, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387130023, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387130217, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387130417, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901387131130, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387131372, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387131560, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387131946, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901387132613, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387133209, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751901387133355, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751901387134284, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387134745, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387134796, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387135594, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387135772, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387136336, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387136540, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751901387136990, "dur": 215021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387097094, "dur": 19967, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387117062, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387117243, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387117578, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_681D101710621A59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387117669, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387117756, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387118197, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751901387118405, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901387118491, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751901387118628, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901387118754, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901387119024, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751901387119170, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387119250, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387119329, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751901387119605, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387119841, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387119993, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387120250, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387120755, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387121158, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387121373, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387121606, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387121817, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387122135, "dur": 826, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Ports\\ControlInputDefinition.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751901387122036, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387123298, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387123532, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387123864, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387124257, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387124963, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387125205, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387125430, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387125650, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387125953, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387126155, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387126394, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387126622, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387126825, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387127040, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387127237, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387127442, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387127677, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387127825, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387128250, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387128442, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387129002, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387129228, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901387129802, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387130037, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387130403, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901387131162, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387131316, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751901387131607, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751901387132174, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387132281, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751901387132488, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387133189, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387134290, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387134772, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387135587, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387135769, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387136351, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387136535, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751901387136990, "dur": 214565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751901387359380, "dur": 2147, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 799, "ts": 1751901387391721, "dur": 3450, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 799, "ts": 1751901387395218, "dur": 4219, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 799, "ts": 1751901387382897, "dur": 18393, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}